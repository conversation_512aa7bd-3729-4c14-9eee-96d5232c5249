2025-08-05 14:00:20,052 - Log<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> initialized using file 'velocity.log'
2025-08-05 14:00:20,052 - Initializing Velocity, Calling init()...
2025-08-05 14:00:20,052 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-05 14:00:20,052 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-05 14:00:20,052 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-05 14:00:20,052 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-05 14:00:20,052 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-05 14:00:20,052 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-05 14:00:20,055 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-05 14:00:20,056 - Do unicode file recognition:  false
2025-08-05 14:00:20,056 - FileResourceLoader : adding path '.'
2025-08-05 14:00:20,067 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-05 14:00:20,069 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-05 14:00:20,070 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-05 14:00:20,070 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-05 14:00:20,071 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-05 14:00:20,071 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-05 14:00:20,072 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-05 14:00:20,073 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-05 14:00:20,073 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-05 14:00:20,074 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-05 14:00:20,085 - Created '20' parsers.
2025-08-05 14:00:20,087 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-05 14:00:20,087 - Velocimacro : Default library not found.
2025-08-05 14:00:20,087 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-05 14:00:20,087 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-05 14:00:20,088 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-05 14:00:20,088 - Velocimacro : autoload off : VM system will not automatically reload global library macros
