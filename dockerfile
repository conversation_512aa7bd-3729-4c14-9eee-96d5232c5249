# 基础镜像
FROM hub.inksyun.com/inksdev/openjdk:8-jre
# 安装 envsubst 命令所在的工具包
USER root
RUN apt-get update && apt-get install -y gettext && rm -rf /var/lib/apt/lists/*
# author
MAINTAINER inks

# 挂载目录
VOLUME /home/<USER>
# 创建目录
RUN mkdir -p /home/<USER>
# 指定路径
WORKDIR /home/<USER>

ENV PARAMS="--server.port=8080 --spring.profiles.active=prod"
RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

# 复制jar文件到路径
COPY ./target/*.jar /home/<USER>/app.jar
EXPOSE 8080
# 启动文件服务
# 增加代码
COPY ./application-prod-temp.yml /home/<USER>/application-prod-temp.yml

# 启动脚本
COPY ./entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint.sh
ENTRYPOINT ["entrypoint.sh"]